import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Edit, Trash2, X, Upload, Calendar, MapPin, Users, GraduationCap, Globe } from 'lucide-react';
import AuthenticatedLayout from '@/layouts/authenticated-layout';
import { Head, useForm, router } from '@inertiajs/react';
import { usePage } from '@inertiajs/react';

interface Kegiatan {
    id: number;
    nama_kegiatan: string;
    deskripsi: string;
    foto: string | null;
    tanggal_kegiatan?: string;
    jenis_kegiatan?: 'siswa' | 'guru' | 'umum';
    lokasi?: string;
}

interface GaleriProps {
    kegiatan?: Kegiatan[];
}

export default function Galeri() {
    const props = usePage().props as unknown as { kegiatan?: any[] };
    const kegiatan = props.kegiatan ?? [];

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<Kegiatan | null>(null);
    const [previewImage, setPreviewImage] = useState<string | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        nama_kegiatan: '',
        deskripsi: '',
        foto: null as File | null,
        tanggal_kegiatan: '',
        jenis_kegiatan: 'umum' as 'siswa' | 'guru' | 'umum',
        lokasi: ''
    });

    const openModal = (item?: Kegiatan) => {
        if (item) {
            setEditingItem(item);
            setData({
                nama_kegiatan: item.nama_kegiatan,
                deskripsi: item.deskripsi,
                foto: null,
                tanggal_kegiatan: item.tanggal_kegiatan || '',
                jenis_kegiatan: item.jenis_kegiatan || 'umum',
                lokasi: item.lokasi || ''
            });
        } else {
            setEditingItem(null);
            reset();
        }
        setPreviewImage(null);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
        setEditingItem(null);
        setPreviewImage(null);
        reset();
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('foto', file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreviewImage(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (editingItem) {
            put(`/update/kegiatan/${editingItem.id}`, {
                onSuccess: () => closeModal(),
            });
        } else {
            post('/add/kegiatan', {
                onSuccess: () => closeModal(),
            });
        }
    };

    const handleDelete = (id: number) => {
        if (confirm('Apakah Anda yakin ingin menghapus kegiatan ini?')) {
            router.delete(`/delete/kegiatan/${id}`, {
                onSuccess: () => {
                    // Optional: Show success message
                }
            });
        }
    };

    const getJenisIcon = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return <Users className="w-4 h-4 text-blue-600" />;
            case 'guru':
                return <GraduationCap className="w-4 h-4 text-green-600" />;
            default:
                return <Globe className="w-4 h-4 text-purple-600" />;
        }
    };

    const getJenisLabel = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return 'Kegiatan Siswa';
            case 'guru':
                return 'Kegiatan Guru';
            default:
                return 'Kegiatan Umum';
        }
    };

    const getJenisBadgeColor = (jenis: string) => {
        switch (jenis) {
            case 'siswa':
                return 'bg-blue-100 text-blue-800';
            case 'guru':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-purple-100 text-purple-800';
        }
    };

    return (
        <AuthenticatedLayout>
            <Head title="Galeri Kegiatan" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        {/* Header */}
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h2 className="text-2xl font-bold text-gray-900">Galeri Kegiatan</h2>
                                    <p className="text-gray-600 mt-1">Kelola foto dan informasi kegiatan sekolah</p>
                                </div>
                                <motion.button
                                    onClick={() => openModal()}
                                    className="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Tambah Kegiatan
                                </motion.button>
                            </div>
                        </div>

                        {/* Table */}
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Foto
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Nama Kegiatan
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Deskripsi
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Jenis
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Tanggal
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Lokasi
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Aksi
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {kegiatan.length > 0 ? (
                                        kegiatan.map((item, index) => (
                                            <motion.tr
                                                key={item.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                className="hover:bg-gray-50"
                                            >
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex-shrink-0 h-16 w-16">
                                                        {item.foto ? (
                                                            <img
                                                                className="h-16 w-16 rounded-lg object-cover"
                                                                src={item.foto}
                                                                alt={item.nama_kegiatan}
                                                            />
                                                        ) : (
                                                            <div className="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                                                                <Upload className="w-6 h-6 text-gray-400" />
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {item.nama_kegiatan}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="text-sm text-gray-900 max-w-xs truncate">
                                                        {item.deskripsi}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getJenisBadgeColor(item.jenis_kegiatan || 'umum')}`}>
                                                        {getJenisIcon(item.jenis_kegiatan || 'umum')}
                                                        <span className="ml-1">{getJenisLabel(item.jenis_kegiatan || 'umum')}</span>
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                                                        {item.tanggal_kegiatan || '-'}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                                                        {item.lokasi || '-'}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="flex space-x-2">
                                                        <button
                                                            onClick={() => openModal(item)}
                                                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded-md hover:bg-indigo-50"
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </button>
                                                        <button
                                                            onClick={() => handleDelete(item.id)}
                                                            className="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-red-50"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </motion.tr>
                                        ))
                                    ) : (
                                        <tr>
                                            <td colSpan={7} className="px-6 py-12 text-center">
                                                <div className="text-gray-500">
                                                    <Upload className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                                                    <p className="text-lg font-medium">Belum ada kegiatan</p>
                                                    <p className="text-sm">Tambahkan kegiatan pertama Anda</p>
                                                </div>
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Modal */}
            <AnimatePresence>
                {isModalOpen && (
                    <div className="fixed inset-0 z-50 overflow-y-auto">
                        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                            <motion.div
                                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                onClick={closeModal}
                            />

                            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
                                &#8203;
                            </span>

                            <motion.div
                                className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"
                                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                                animate={{ opacity: 1, scale: 1, y: 0 }}
                                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                            >
                                <div className="absolute top-0 right-0 pt-4 pr-4">
                                    <button
                                        type="button"
                                        className="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        onClick={closeModal}
                                    >
                                        <span className="sr-only">Close</span>
                                        <X className="h-6 w-6" />
                                    </button>
                                </div>

                                <div className="sm:flex sm:items-start">
                                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                            {editingItem ? 'Edit Kegiatan' : 'Tambah Kegiatan Baru'}
                                        </h3>

                                        <form onSubmit={handleSubmit} className="space-y-4">
                                            {/* Upload Foto */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Foto Kegiatan
                                                </label>
                                                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                                                    <div className="space-y-1 text-center">
                                                        {previewImage ? (
                                                            <div className="relative">
                                                                <img
                                                                    src={previewImage}
                                                                    alt="Preview"
                                                                    className="mx-auto h-32 w-32 object-cover rounded-lg"
                                                                />
                                                                <button
                                                                    type="button"
                                                                    onClick={() => {
                                                                        setPreviewImage(null);
                                                                        setData('foto', null);
                                                                    }}
                                                                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                                                >
                                                                    <X className="w-3 h-3" />
                                                                </button>
                                                            </div>
                                                        ) : (
                                                            <>
                                                                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                                                <div className="flex text-sm text-gray-600">
                                                                    <label
                                                                        htmlFor="foto"
                                                                        className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                                                                    >
                                                                        <span>Upload foto</span>
                                                                        <input
                                                                            id="foto"
                                                                            name="foto"
                                                                            type="file"
                                                                            className="sr-only"
                                                                            accept="image/*"
                                                                            onChange={handleImageChange}
                                                                        />
                                                                    </label>
                                                                    <p className="pl-1">atau drag and drop</p>
                                                                </div>
                                                                <p className="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                                {errors.foto && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.foto}</p>
                                                )}
                                            </div>

                                            {/* Nama Kegiatan */}
                                            <div>
                                                <label htmlFor="nama_kegiatan" className="block text-sm font-medium text-gray-700">
                                                    Nama Kegiatan
                                                </label>
                                                <input
                                                    type="text"
                                                    id="nama_kegiatan"
                                                    value={data.nama_kegiatan}
                                                    onChange={(e) => setData('nama_kegiatan', e.target.value)}
                                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                    placeholder="Masukkan nama kegiatan"
                                                />
                                                {errors.nama_kegiatan && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.nama_kegiatan}</p>
                                                )}
                                            </div>

                                            {/* Deskripsi */}
                                            <div>
                                                <label htmlFor="deskripsi" className="block text-sm font-medium text-gray-700">
                                                    Deskripsi
                                                </label>
                                                <textarea
                                                    id="deskripsi"
                                                    rows={3}
                                                    value={data.deskripsi}
                                                    onChange={(e) => setData('deskripsi', e.target.value)}
                                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                    placeholder="Masukkan deskripsi kegiatan"
                                                />
                                                {errors.deskripsi && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.deskripsi}</p>
                                                )}
                                            </div>

                                            {/* Jenis Kegiatan */}
                                            <div>
                                                <label htmlFor="jenis_kegiatan" className="block text-sm font-medium text-gray-700">
                                                    Jenis Kegiatan
                                                </label>
                                                <select
                                                    id="jenis_kegiatan"
                                                    value={data.jenis_kegiatan}
                                                    onChange={(e) => setData('jenis_kegiatan', e.target.value as 'siswa' | 'guru' | 'umum')}
                                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                >
                                                    <option value="umum">Kegiatan Umum</option>
                                                    <option value="siswa">Kegiatan Siswa</option>
                                                    <option value="guru">Kegiatan Guru</option>
                                                </select>
                                                {errors.jenis_kegiatan && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.jenis_kegiatan}</p>
                                                )}
                                            </div>

                                            {/* Tanggal Kegiatan */}
                                            <div>
                                                <label htmlFor="tanggal_kegiatan" className="block text-sm font-medium text-gray-700">
                                                    Tanggal Kegiatan
                                                </label>
                                                <input
                                                    type="date"
                                                    id="tanggal_kegiatan"
                                                    value={data.tanggal_kegiatan}
                                                    onChange={(e) => setData('tanggal_kegiatan', e.target.value)}
                                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                />
                                                {errors.tanggal_kegiatan && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.tanggal_kegiatan}</p>
                                                )}
                                            </div>

                                            {/* Lokasi */}
                                            <div>
                                                <label htmlFor="lokasi" className="block text-sm font-medium text-gray-700">
                                                    Lokasi
                                                </label>
                                                <input
                                                    type="text"
                                                    id="lokasi"
                                                    value={data.lokasi}
                                                    onChange={(e) => setData('lokasi', e.target.value)}
                                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                    placeholder="Masukkan lokasi kegiatan"
                                                />
                                                {errors.lokasi && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.lokasi}</p>
                                                )}
                                            </div>

                                            {/* Submit Buttons */}
                                            <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                                                <button
                                                    type="submit"
                                                    disabled={processing}
                                                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                                >
                                                    {processing ? 'Menyimpan...' : (editingItem ? 'Update' : 'Simpan')}
                                                </button>
                                                <button
                                                    type="button"
                                                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
                                                    onClick={closeModal}
                                                >
                                                    Batal
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                )}
            </AnimatePresence>
        </AuthenticatedLayout>
    );
}